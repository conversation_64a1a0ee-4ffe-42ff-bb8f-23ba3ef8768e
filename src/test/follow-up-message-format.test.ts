/**
 * Test file để kiểm tra cấu trúc follow-up message mới
 * Chạy test này để đảm bảo cấu trúc JSON từ database được xử lý đúng
 */

import { FollowUpRule, MessageFormatting, MessageButton } from '../services/queue/types/follow-up.types';

// Mô phỏng dữ liệu từ Supabase
const sampleFollowUpRules: FollowUpRule[] = [
  {
    id: "rule_test",
    order: 1,
    message: "Test message with image",
    is_enabled: true,
    message_type: "formatted",
    delay_minutes: 1,
    created_at: "2025-07-10T06:26:07.479Z",
    message_formatting: {
      images: ["https://supabase.mooly.vn/storage/v1/object/public/product-loma/hero-landing-page-banner/loma-bag-bst-ha.jpg"],
      buttons: []
    }
  },
  {
    id: "rule_1752128767479_hd95iqg6l",
    order: 2,
    message: "Message with image and button",
    created_at: "2025-07-10T06:26:07.479Z",
    is_enabled: true,
    message_type: "formatted",
    delay_minutes: 23,
    message_formatting: {
      images: ["https://supabase.mooly.vn/storage/v1/object/public/product-loma/hero-landing-page-banner/loma-bag-bst-ha.jpg"],
      buttons: [
        {
          id: "btn_1752129374141_m04a0be1b",
          url: "https://example.com",
          type: "link",
          title: "Xem thêm"
        }
      ]
    }
  },
  {
    id: "rule_text_only",
    order: 3,
    message: "Simple text message",
    is_enabled: true,
    message_type: "text",
    delay_minutes: 5,
    created_at: "2025-07-10T06:26:07.479Z"
  }
];

/**
 * Test function để kiểm tra cấu trúc message formatting
 */
function testMessageFormatting() {
  console.log('🧪 Testing Follow-up Message Formatting...\n');

  sampleFollowUpRules.forEach((rule, index) => {
    console.log(`📋 Rule ${index + 1}: ${rule.id}`);
    console.log(`   Message: ${rule.message}`);
    console.log(`   Type: ${rule.message_type || 'text'}`);
    console.log(`   Delay: ${rule.delay_minutes} minutes`);
    
    if (rule.message_type === 'formatted' && rule.message_formatting) {
      const formatting = rule.message_formatting;
      
      if (formatting.images && formatting.images.length > 0) {
        console.log(`   📸 Images (${formatting.images.length}):`);
        formatting.images.forEach((img, i) => {
          console.log(`      ${i + 1}. ${img}`);
        });
      }
      
      if (formatting.buttons && formatting.buttons.length > 0) {
        console.log(`   🔘 Buttons (${formatting.buttons.length}):`);
        formatting.buttons.forEach((btn, i) => {
          console.log(`      ${i + 1}. ${btn.title} (${btn.type})`);
          if (btn.type === 'link' && btn.url) {
            console.log(`         URL: ${btn.url}`);
          }
          if (btn.type === 'postback' && btn.payload) {
            console.log(`         Payload: ${btn.payload}`);
          }
        });
      }
    }
    
    console.log('');
  });
}

/**
 * Test function để mô phỏng việc tạo Chatwoot message body
 */
function testChatwootMessageBody() {
  console.log('🔧 Testing Chatwoot Message Body Generation...\n');

  sampleFollowUpRules.forEach((rule) => {
    console.log(`📋 Processing rule: ${rule.id}`);
    
    // Mô phỏng logic tạo message body như trong processor
    let messageBody: any = {
      content: rule.message,
      private: false,
    };
    
    // Nếu có buttons, thêm vào content_attributes
    if (rule.message_type === 'formatted' && rule.message_formatting?.buttons && rule.message_formatting.buttons.length > 0) {
      messageBody = {
        content: rule.message,
        content_type: "input_select",
        content_attributes: {
          items: rule.message_formatting.buttons.map(button => ({
            title: button.title,
            value: button.type === 'link' ? button.url : button.payload || button.id
          }))
        },
        private: false
      };
    }
    
    console.log('   Generated message body:');
    console.log('   ', JSON.stringify(messageBody, null, 2));
    console.log('');
  });
}

// Chạy tests
if (require.main === module) {
  testMessageFormatting();
  testChatwootMessageBody();
  console.log('✅ All tests completed!');
}

export {
  sampleFollowUpRules,
  testMessageFormatting,
  testChatwootMessageBody
};
