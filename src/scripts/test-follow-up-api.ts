/**
 * Script test Follow-up API
 * Chạy script này để test các API follow-up message
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api/test/follow-up';

// Cấu hình test (thay đổi theo môi trường thực tế)
const TEST_CONFIG = {
  accountId: '1',
  conversationId: '123',
  tenantId: 'your-tenant-uuid',
  channelId: 'your-channel-uuid'
};

/**
 * Test gửi tin nhắn text đơn giản
 */
async function testSendTextMessage() {
  console.log('🧪 Testing send text message...');
  
  try {
    const response = await fetch(`${BASE_URL}/send-message`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        accountId: TEST_CONFIG.accountId,
        conversationId: TEST_CONFIG.conversationId,
        content: 'Đ<PERSON><PERSON> là tin nhắn follow-up test từ API',
        messageType: 'text'
      })
    });
    
    const result = await response.json();
    console.log('✅ Text message result:', result);
    
  } catch (error) {
    console.error('❌ Text message error:', error);
  }
}

/**
 * Test gửi tin nhắn có hình ảnh
 */
async function testSendImageMessage() {
  console.log('🧪 Testing send image message...');
  
  try {
    const response = await fetch(`${BASE_URL}/send-message`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        accountId: TEST_CONFIG.accountId,
        conversationId: TEST_CONFIG.conversationId,
        content: 'Đây là sản phẩm mới của chúng tôi',
        messageType: 'formatted',
        images: ['https://supabase.mooly.vn/storage/v1/object/public/product-loma/hero-landing-page-banner/loma-bag-bst-ha.jpg']
      })
    });
    
    const result = await response.json();
    console.log('✅ Image message result:', result);
    
  } catch (error) {
    console.error('❌ Image message error:', error);
  }
}

/**
 * Test gửi tin nhắn có nút
 */
async function testSendButtonMessage() {
  console.log('🧪 Testing send button message...');
  
  try {
    const response = await fetch(`${BASE_URL}/send-message`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        accountId: TEST_CONFIG.accountId,
        conversationId: TEST_CONFIG.conversationId,
        content: 'Chọn một trong các tùy chọn bên dưới',
        messageType: 'formatted',
        buttons: [
          {
            id: 'btn_1',
            title: 'Xem sản phẩm',
            type: 'link',
            url: 'https://mooly.vn'
          },
          {
            id: 'btn_2',
            title: 'Liên hệ tư vấn',
            type: 'postback',
            payload: 'CONTACT_SUPPORT'
          }
        ]
      })
    });
    
    const result = await response.json();
    console.log('✅ Button message result:', result);
    
  } catch (error) {
    console.error('❌ Button message error:', error);
  }
}

/**
 * Test gửi tin nhắn đầy đủ (hình ảnh + nút)
 */
async function testSendFullMessage() {
  console.log('🧪 Testing send full message...');
  
  try {
    const response = await fetch(`${BASE_URL}/send-message`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        accountId: TEST_CONFIG.accountId,
        conversationId: TEST_CONFIG.conversationId,
        content: 'Sản phẩm hot nhất tháng này!',
        messageType: 'formatted',
        images: ['https://supabase.mooly.vn/storage/v1/object/public/product-loma/hero-landing-page-banner/loma-bag-bst-ha.jpg'],
        buttons: [
          {
            id: 'btn_buy',
            title: 'Mua ngay',
            type: 'link',
            url: 'https://mooly.vn/buy'
          },
          {
            id: 'btn_info',
            title: 'Xem thông tin',
            type: 'postback',
            payload: 'VIEW_INFO'
          }
        ]
      })
    });
    
    const result = await response.json();
    console.log('✅ Full message result:', result);
    
  } catch (error) {
    console.error('❌ Full message error:', error);
  }
}

/**
 * Test gửi sample messages
 */
async function testSendSampleMessages() {
  console.log('🧪 Testing send sample messages...');
  
  const sampleTypes = ['text', 'image', 'button', 'full'];
  
  for (const sampleType of sampleTypes) {
    try {
      console.log(`   Testing sample type: ${sampleType}`);
      
      const response = await fetch(`${BASE_URL}/send-sample`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          accountId: TEST_CONFIG.accountId,
          conversationId: TEST_CONFIG.conversationId,
          sampleType
        })
      });
      
      const result = await response.json();
      console.log(`   ✅ Sample ${sampleType} result:`, result);
      
      // Delay giữa các tin nhắn
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error(`   ❌ Sample ${sampleType} error:`, error);
    }
  }
}

/**
 * Test lấy cấu hình follow-up
 */
async function testGetConfig() {
  console.log('🧪 Testing get follow-up config...');
  
  try {
    const response = await fetch(`${BASE_URL}/config/${TEST_CONFIG.channelId}`);
    const result = await response.json();
    console.log('✅ Config result:', result);
    
  } catch (error) {
    console.error('❌ Config error:', error);
  }
}

/**
 * Test lấy trạng thái follow-up
 */
async function testGetState() {
  console.log('🧪 Testing get follow-up state...');
  
  try {
    const response = await fetch(`${BASE_URL}/state/${TEST_CONFIG.conversationId}`);
    const result = await response.json();
    console.log('✅ State result:', result);
    
  } catch (error) {
    console.error('❌ State error:', error);
  }
}

/**
 * Test tạo follow-up job
 */
async function testCreateJob() {
  console.log('🧪 Testing create follow-up job...');
  
  try {
    const response = await fetch(`${BASE_URL}/create-job`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        conversationId: TEST_CONFIG.conversationId,
        tenantId: TEST_CONFIG.tenantId,
        channelId: TEST_CONFIG.channelId,
        accountId: TEST_CONFIG.accountId,
        ruleIndex: 0,
        resetState: true
      })
    });
    
    const result = await response.json();
    console.log('✅ Create job result:', result);
    
  } catch (error) {
    console.error('❌ Create job error:', error);
  }
}

/**
 * Chạy tất cả tests
 */
async function runAllTests() {
  console.log('🚀 Starting Follow-up API Tests...\n');
  
  // Test gửi tin nhắn
  await testSendTextMessage();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testSendImageMessage();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testSendButtonMessage();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testSendFullMessage();
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test sample messages
  await testSendSampleMessages();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test config và state
  await testGetConfig();
  await testGetState();
  
  // Test create job (chỉ test nếu có đủ thông tin)
  if (TEST_CONFIG.tenantId !== 'your-tenant-uuid') {
    await testCreateJob();
  } else {
    console.log('⚠️ Skipping create job test - please update TEST_CONFIG');
  }
  
  console.log('\n✅ All tests completed!');
}

// Chạy tests nếu file được execute trực tiếp
if (require.main === module) {
  runAllTests().catch(console.error);
}

export {
  testSendTextMessage,
  testSendImageMessage,
  testSendButtonMessage,
  testSendFullMessage,
  testSendSampleMessages,
  testGetConfig,
  testGetState,
  testCreateJob,
  runAllTests
};
