/**
 * Follow-up Job Types - Đ<PERSON><PERSON> ngh<PERSON>a các types cho follow-up automation
 */

export interface FollowUpRule {
  id: string;
  order: number;
  message: string;
  delay_minutes: number;
  is_enabled: boolean;
  created_at: string;
}

export interface ChannelAutomationFollowup {
  id: string;
  tenant_id: string;
  channel_id: string;
  followup_rules: FollowUpRule[];
  is_enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface ConversationFollowUpState {
  conversation_id: string;
  tenant_id: string;
  channel_id: string;
  last_customer_message_at: string;
  current_rule_index: number;
  sent_rule_ids: string[];
  active_job_id?: string;
  is_active: boolean;
}

export interface FollowUpJobData {
  conversation_id: string;
  tenant_id: string;
  channel_id: string;
  account_id: string;
  rule_id: string;
  rule_order: number;
  message: string;
  next_rule?: {
    id: string;
    order: number;
    message: string;
    delay_minutes: number;
  };
}

export interface FollowUpJobResult {
  success: boolean;
  conversation_id: string;
  rule_id: string;
  message_sent: boolean;
  next_job_created: boolean;
  next_job_id?: string;
  error?: string;
}

export interface CreateFollowUpJobOptions {
  conversation_id: string;
  tenant_id: string;
  channel_id: string;
  account_id: string;
  rule_index?: number; // Mặc định là 0 (rule đầu tiên)
  reset_state?: boolean; // Reset về rule đầu tiên
}

export interface CancelFollowUpJobOptions {
  conversation_id: string;
  tenant_id: string;
  reason?: string;
}

export interface FollowUpServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Job names cho follow-up queue
export const FOLLOW_UP_JOB_NAMES = {
  SEND_FOLLOW_UP: 'send-follow-up',
  CANCEL_FOLLOW_UP: 'cancel-follow-up',
  RESET_FOLLOW_UP: 'reset-follow-up',
} as const;

export type FollowUpJobName = keyof typeof FOLLOW_UP_JOB_NAMES;
