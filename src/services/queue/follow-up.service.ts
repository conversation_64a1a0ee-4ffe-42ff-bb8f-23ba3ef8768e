/**
 * Follow-up Service - <PERSON><PERSON>ản lý logic follow-up automation
 * Tích hợp với BullMQ để tạo, hủy và track follow-up jobs
 */

import {
  FollowUpJobData,
  CreateFollowUpJobOptions,
  CancelFollowUpJobOptions,
  FollowUpServiceResult,
  ConversationFollowUpState,
  ChannelAutomationFollowup,
  FOLLOW_UP_JOB_NAMES
} from './types/follow-up.types';
import { Queue } from 'bullmq';
import Redis from 'ioredis';
import { QUEUE_NAMES, queueConfigs, messageBufferRedisConnection } from './config';
import { supabaseAdmin } from '../../config/supabase';

/**
 * Follow-up Queue instance
 */
let followUpQueue: Queue | null = null;

/**
 * Redis instance cho follow-up state management
 * Sử dụng cùng cấu hình với messageBufferRedisConnection để đồng bộ
 */
let followUpRedis: Redis | null = null;

/**
 * Khởi tạo Redis instance cho follow-up
 * Sử dụng pattern tương tự MessageBufferService
 */
const getFollowUpRedis = (): Redis => {
  if (!followUpRedis) {
    followUpRedis = new Redis(messageBufferRedisConnection as any);

    // Setup event handlers tương tự MessageBufferService
    followUpRedis.on('connect', () => {
      console.log('✅ Follow-up Redis connected');
    });

    followUpRedis.on('ready', () => {
      console.log('✅ Follow-up Redis ready');
    });

    followUpRedis.on('error', (error) => {
      console.error('❌ Follow-up Redis connection error:', error);
    });

    followUpRedis.on('close', () => {
      console.warn('⚠️ Follow-up Redis connection closed');
    });

    followUpRedis.on('reconnecting', () => {
      console.log('🔄 Follow-up Redis reconnecting...');
    });
  }
  return followUpRedis;
};

/**
 * Khởi tạo Follow-up Queue
 */
export const initializeFollowUpQueue = (): Queue => {
  if (!followUpQueue) {
    followUpQueue = new Queue(QUEUE_NAMES.FOLLOW_UP, {
      ...queueConfigs[QUEUE_NAMES.FOLLOW_UP],
    });
    
    console.log('✅ Follow-up Queue đã được khởi tạo');
  }
  
  return followUpQueue;
};

/**
 * Lấy Follow-up Queue instance
 */
export const getFollowUpQueue = (): Queue => {
  if (!followUpQueue) {
    return initializeFollowUpQueue();
  }
  return followUpQueue;
};

/**
 * Redis keys cho follow-up state management
 */
const getFollowUpStateKey = (conversationId: string) => `followup:state:${conversationId}`;

/**
 * Lấy cấu hình follow-up cho channel
 */
export const getChannelFollowUpConfig = async (
  channelId: string, 
  tenantId: string
): Promise<FollowUpServiceResult<ChannelAutomationFollowup>> => {
  try {
    const { data, error } = await supabaseAdmin
      .from('channel_automation_followup')
      .select('*')
      .eq('channel_id', channelId)
      .eq('tenant_id', tenantId)
      .eq('is_enabled', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return { success: false, message: 'Không tìm thấy cấu hình follow-up cho channel' };
      }
      throw error;
    }

    return { success: true, data };
  } catch (error: any) {
    console.error('❌ Lỗi khi lấy cấu hình follow-up:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Lấy trạng thái follow-up của conversation từ Redis
 */
export const getConversationFollowUpState = async (
  conversationId: string
): Promise<ConversationFollowUpState | null> => {
  try {
    const redis = getFollowUpRedis();
    const stateKey = getFollowUpStateKey(conversationId);
    const stateData = await redis.get(stateKey);

    if (!stateData) {
      return null;
    }

    return JSON.parse(stateData) as ConversationFollowUpState;
  } catch (error: any) {
    console.error('❌ Lỗi khi lấy trạng thái follow-up:', error);
    return null;
  }
};

/**
 * Lưu trạng thái follow-up của conversation vào Redis
 */
export const saveConversationFollowUpState = async (
  state: ConversationFollowUpState
): Promise<boolean> => {
  try {
    const redis = getFollowUpRedis();
    const stateKey = getFollowUpStateKey(state.conversation_id);
    const ttl = 7 * 24 * 60 * 60; // 7 ngày

    await redis.setex(stateKey, ttl, JSON.stringify(state));
    return true;
  } catch (error: any) {
    console.error('❌ Lỗi khi lưu trạng thái follow-up:', error);
    return false;
  }
};

/**
 * Hủy job follow-up hiện tại của conversation
 */
export const cancelActiveFollowUpJob = async (
  conversationId: string
): Promise<boolean> => {
  try {
    const state = await getConversationFollowUpState(conversationId);
    
    if (!state || !state.active_job_id) {
      console.log(`⚠️ Không có job active cho conversation ${conversationId}`);
      return true;
    }

    const queue = getFollowUpQueue();
    const job = await queue.getJob(state.active_job_id);
    
    if (job) {
      await job.remove();
      console.log(`🗑️ Đã hủy follow-up job ${state.active_job_id} cho conversation ${conversationId}`);
    }

    // Xóa job ID khỏi state
    state.active_job_id = undefined;
    await saveConversationFollowUpState(state);
    
    return true;
  } catch (error: any) {
    console.error('❌ Lỗi khi hủy follow-up job:', error);
    return false;
  }
};

/**
 * Tạo follow-up job mới
 */
export const createFollowUpJob = async (
  options: CreateFollowUpJobOptions
): Promise<FollowUpServiceResult<{ jobId: string; ruleId: string }>> => {
  try {
    const { conversation_id, tenant_id, channel_id, account_id, rule_index = 0, reset_state = false } = options;

    // Lấy cấu hình follow-up cho channel
    const configResult = await getChannelFollowUpConfig(channel_id, tenant_id);
    if (!configResult.success || !configResult.data) {
      return { success: false, error: 'Không tìm thấy cấu hình follow-up cho channel' };
    }

    const config = configResult.data;
    const enabledRules = config.followup_rules
      .filter(rule => rule.is_enabled)
      .sort((a, b) => a.order - b.order);

    if (enabledRules.length === 0) {
      return { success: false, error: 'Không có rule follow-up nào được kích hoạt' };
    }

    if (rule_index >= enabledRules.length) {
      return { success: false, error: 'Rule index vượt quá số lượng rules có sẵn' };
    }

    // Hủy job hiện tại nếu có
    await cancelActiveFollowUpJob(conversation_id);

    const currentRule = enabledRules[rule_index];
    const nextRule = rule_index + 1 < enabledRules.length ? enabledRules[rule_index + 1] : undefined;

    // Lấy hoặc tạo state mới
    let state = await getConversationFollowUpState(conversation_id);
    
    if (!state || reset_state) {
      state = {
        conversation_id,
        tenant_id,
        channel_id,
        last_customer_message_at: new Date().toISOString(),
        current_rule_index: rule_index,
        sent_rule_ids: [],
        is_active: true,
      };
    } else {
      // Cập nhật state hiện tại
      state.last_customer_message_at = new Date().toISOString();
      state.current_rule_index = rule_index;
      state.is_active = true;
    }

    // Xử lý message_formatting (convert null to undefined)
    const processedCurrentRule = {
      ...currentRule,
      message_formatting: currentRule.message_formatting === null ? undefined : currentRule.message_formatting
    };

    const processedNextRule = nextRule ? {
      ...nextRule,
      message_formatting: nextRule.message_formatting === null ? undefined : nextRule.message_formatting
    } : undefined;



    // Tạo job data
    const jobData: FollowUpJobData = {
      conversation_id,
      tenant_id,
      channel_id,
      account_id,
      rule_id: processedCurrentRule.id,
      rule_order: processedCurrentRule.order,
      message: processedCurrentRule.message,
      message_formatting: processedCurrentRule.message_formatting,
      next_rule: processedNextRule ? {
        id: processedNextRule.id,
        order: processedNextRule.order,
        message: processedNextRule.message,
        delay_minutes: processedNextRule.delay_minutes,
        message_formatting: processedNextRule.message_formatting,
      } : undefined,
    };

    // Tạo job với delay
    const queue = getFollowUpQueue();
    const delayMs = currentRule.delay_minutes * 60 * 1000;
    
    const job = await queue.add(
      FOLLOW_UP_JOB_NAMES.SEND_FOLLOW_UP,
      jobData,
      {
        delay: delayMs,
        priority: 8,
        jobId: `followup:${conversation_id}:${currentRule.id}:${Date.now()}`,
      }
    );

    // Cập nhật state với job ID
    state.active_job_id = job.id;
    await saveConversationFollowUpState(state);

    console.log(`📅 Đã tạo follow-up job ${job.id} cho conversation ${conversation_id}, rule ${currentRule.order}, delay ${currentRule.delay_minutes} phút`);

    return {
      success: true,
      data: {
        jobId: job.id || '',
        ruleId: currentRule.id,
      },
      message: `Follow-up job đã được tạo với delay ${currentRule.delay_minutes} phút`,
    };

  } catch (error: any) {
    console.error('❌ Lỗi khi tạo follow-up job:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Hủy tất cả follow-up jobs cho conversation
 */
export const cancelFollowUpJobs = async (
  options: CancelFollowUpJobOptions
): Promise<FollowUpServiceResult<void>> => {
  try {
    const { conversation_id, reason = 'Customer interaction' } = options;

    const success = await cancelActiveFollowUpJob(conversation_id);
    
    if (success) {
      console.log(`🛑 Đã hủy follow-up jobs cho conversation ${conversation_id}. Lý do: ${reason}`);
      return { success: true, message: 'Follow-up jobs đã được hủy thành công' };
    } else {
      return { success: false, error: 'Không thể hủy follow-up jobs' };
    }

  } catch (error: any) {
    console.error('❌ Lỗi khi hủy follow-up jobs:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Đóng Follow-up Queue
 */
export const closeFollowUpQueue = async (): Promise<void> => {
  if (followUpQueue) {
    await followUpQueue.close();
    followUpQueue = null;
    console.log('👋 Follow-up Queue đã được đóng');
  }
};

export default {
  initializeFollowUpQueue,
  getFollowUpQueue,
  getChannelFollowUpConfig,
  createFollowUpJob,
  cancelFollowUpJobs,
  getConversationFollowUpState,
  closeFollowUpQueue,
};
