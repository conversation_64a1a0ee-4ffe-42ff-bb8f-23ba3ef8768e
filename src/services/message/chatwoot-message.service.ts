/**
 * Chatwoot Message Service - Xử lý gửi tin nhắn tới Chatwoot
 * Hỗ trợ gửi text, hình ảnh và tin nhắn có nút
 */

import { MessageFormatting, MessageButton } from '../queue/types/follow-up.types';
import { getMoolyAccount } from '../postgres/chatbot.service';

export interface SendMessageOptions {
  accountId: string;
  conversationId: string;
  content: string;
  messageType?: 'text' | 'formatted';
  messageFormatting?: MessageFormatting;
  apiToken?: string;
}

export interface SendMessageResult {
  success: boolean;
  error?: string;
  imagesSent?: number;
  textSent?: boolean;
}

/**
 * Gửi tin nhắn tới Chatwoot với hỗ trợ hình ảnh và nút
 */
export async function sendChatwootMessage(options: SendMessageOptions): Promise<SendMessageResult> {
  const { accountId, conversationId, content, messageType = 'text', messageFormatting } = options;

  try {
    // Debug log
    console.log(`📤 Sending Chatwoot message:`, {
      messageType,
      hasFormatting: !!messageFormatting,
      hasImages: messageFormatting?.images?.length || 0,
      hasButtons: messageFormatting?.buttons?.length || 0
    });

    // Lấy API token
    const apiToken = await getApiToken(accountId, options.apiToken);

    let imagesSent = 0;
    let textSent = false;

    // Gửi hình ảnh trước (nếu có)
    if (messageType === 'formatted' && messageFormatting?.images && messageFormatting.images.length > 0) {
      console.log(`📸 Đang gửi ${messageFormatting.images.length} hình ảnh...`);

      for (const imageUrl of messageFormatting.images) {
        const imageResult = await sendImageMessage(accountId, conversationId, imageUrl, apiToken);
        if (imageResult) {
          imagesSent++;
        }
      }
    }

    // Xác định buttons để gửi - chỉ gửi nếu messageType là 'formatted' và có buttons
    let buttonsToSend: MessageButton[] | undefined;
    if (messageType === 'formatted' && messageFormatting?.buttons && messageFormatting.buttons.length > 0) {
      buttonsToSend = messageFormatting.buttons;
    }

    // Gửi tin nhắn text với hoặc không có nút
    const textResult = await sendTextMessage(accountId, conversationId, content, buttonsToSend, apiToken);
    textSent = textResult;
    
    return {
      success: textSent,
      imagesSent,
      textSent
    };
    
  } catch (error: any) {
    console.error('❌ Lỗi khi gửi tin nhắn Chatwoot:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Lấy API token từ account hoặc sử dụng token mặc định
 */
async function getApiToken(accountId: string, providedToken?: string): Promise<string> {
  if (providedToken) {
    return providedToken;
  }
  
  let apiToken = process.env.MOOLY_BOT_API_KEY || '';
  
  try {
    const accountResult = await getMoolyAccount({
      accoutnId: accountId,
    });
    if (accountResult.success && accountResult.data?.token) {
      apiToken = accountResult.data.token;
      console.log(`🔑 Sử dụng token từ tài khoản: ${apiToken.substring(0, 10)}...`);
    }
  } catch (error) {
    console.warn('⚠️ Không thể lấy token từ account, sử dụng token mặc định');
  }
  
  return apiToken;
}

/**
 * Gửi tin nhắn hình ảnh
 */
async function sendImageMessage(
  accountId: string,
  conversationId: string,
  imageUrl: string,
  apiToken: string
): Promise<boolean> {
  try {
    const response = await fetch(
      `https://app.mooly.vn/api/v1/accounts/${accountId}/conversations/${conversationId}/messages`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          api_access_token: apiToken,
        },
        body: JSON.stringify({
          content: "",
          attachments: [{
            "file_type": "image",
            "external_url": imageUrl
          }],
          "private": false
        }),
      }
    );
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Không thể gửi hình ảnh:`, response.status, errorText);
      return false;
    }
    
    console.log(`📸 Đã gửi hình ảnh: ${imageUrl}`);
    return true;
    
  } catch (error) {
    console.error(`❌ Lỗi khi gửi hình ảnh:`, error);
    return false;
  }
}

/**
 * Gửi tin nhắn text với hoặc không có nút
 */
async function sendTextMessage(
  accountId: string,
  conversationId: string,
  content: string,
  buttons?: MessageButton[],
  apiToken?: string
): Promise<boolean> {
  try {
    const token = apiToken || process.env.MOOLY_BOT_API_KEY || '';
    
    // Tạo message body
    let messageBody: any = {
      content: content,
      private: false,
    };
    
    // Nếu có buttons, thêm vào content_attributes
    if (buttons && buttons.length > 0) {
      console.log(`🔘 Đang gửi tin nhắn với ${buttons.length} nút...`);
      
      messageBody = {
        content: content,
        content_type: "input_select",
        content_attributes: {
          items: buttons.map(button => ({
            title: button.title,
            value: button.type === 'link' ? button.url : button.payload || button.id
          }))
        },
        private: false
      };
    }
    
    const response = await fetch(
      `https://app.mooly.vn/api/v1/accounts/${accountId}/conversations/${conversationId}/messages`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          api_access_token: token,
        },
        body: JSON.stringify(messageBody),
      }
    );
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Không thể gửi tin nhắn text:`, response.status, errorText);
      return false;
    }
    
    console.log(`📤 Đã gửi tin nhắn: "${content.substring(0, 50)}${content.length > 50 ? '...' : ''}"`);
    return true;
    
  } catch (error) {
    console.error(`❌ Lỗi khi gửi tin nhắn text:`, error);
    return false;
  }
}

/**
 * Tạo message body cho Chatwoot dựa trên formatting
 */
export function createChatwootMessageBody(
  content: string,
  buttons?: MessageButton[]
): any {
  let messageBody: any = {
    content: content,
    private: false,
  };
  
  if (buttons && buttons.length > 0) {
    messageBody = {
      content: content,
      content_type: "input_select",
      content_attributes: {
        items: buttons.map(button => ({
          title: button.title,
          value: button.type === 'link' ? button.url : button.payload || button.id
        }))
      },
      private: false
    };
  }
  
  return messageBody;
}

export default {
  sendChatwootMessage,
  createChatwootMessageBody
};
