/**
 * Follow-up Test Routes - API routes để test hệ thống follow-up messages
 */

import { Router } from 'express';
import {
  testSendFollowUpMessage,
  testCreateFollowUpJob,
  testGetFollowUpConfig,
  testGetFollowUpState,
  testCancelFollowUpJobs,
  testSendSampleMessage
} from '../controllers/follow-up-test.controller';

const router = Router();

/**
 * @route   POST /api/test/follow-up/send-message
 * @desc    Test gửi tin nhắn follow-up đơn lẻ
 * @access  Public
 * @body    {
 *            accountId: string,
 *            conversationId: string,
 *            content: string,
 *            messageType?: 'text' | 'formatted',
 *            images?: string[],
 *            buttons?: Array<{
 *              id: string,
 *              title: string,
 *              type: 'link' | 'postback',
 *              url?: string,
 *              payload?: string
 *            }>
 *          }
 */
router.post('/send-message', testSendFollowUpMessage);

/**
 * @route   POST /api/test/follow-up/create-job
 * @desc    Test tạo follow-up job
 * @access  Public
 * @body    {
 *            conversationId: string,
 *            tenantId: string,
 *            channelId: string,
 *            accountId: string,
 *            ruleIndex?: number,
 *            resetState?: boolean
 *          }
 */
router.post('/create-job', testCreateFollowUpJob);

/**
 * @route   GET /api/test/follow-up/config/:channelId
 * @desc    Lấy cấu hình follow-up của channel
 * @access  Public
 * @param   channelId - ID của channel
 */
router.get('/config/:channelId', testGetFollowUpConfig);

/**
 * @route   GET /api/test/follow-up/state/:conversationId
 * @desc    Lấy trạng thái follow-up của conversation
 * @access  Public
 * @param   conversationId - ID của conversation
 */
router.get('/state/:conversationId', testGetFollowUpState);

/**
 * @route   POST /api/test/follow-up/cancel
 * @desc    Hủy follow-up jobs
 * @access  Public
 * @body    {
 *            conversationId: string,
 *            tenantId: string,
 *            reason?: string
 *          }
 */
router.post('/cancel', testCancelFollowUpJobs);

/**
 * @route   POST /api/test/follow-up/send-sample
 * @desc    Test gửi tin nhắn với sample data
 * @access  Public
 * @body    {
 *            accountId: string,
 *            conversationId: string,
 *            sampleType: 'text' | 'image' | 'button' | 'full'
 *          }
 */
router.post('/send-sample', testSendSampleMessage);

export default router;
