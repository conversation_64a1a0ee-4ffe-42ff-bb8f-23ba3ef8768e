/**
 * Follow-up Test Controller - API để test hệ thống follow-up messages
 */

import { Request, Response } from 'express';
import { sendChatwootMessage } from '../services/message/chatwoot-message.service';
import { 
  createFollowUpJob, 
  getChannelFollowUpConfig,
  getConversationFollowUpState,
  cancelFollowUpJobs
} from '../services/queue/follow-up.service';
import { MessageFormatting, MessageButton } from '../services/queue/types/follow-up.types';

/**
 * Test gửi tin nhắn follow-up đơn lẻ
 * POST /api/test/follow-up/send-message
 */
export const testSendFollowUpMessage = async (req: Request, res: Response) => {
  try {
    const {
      accountId,
      conversationId,
      content,
      messageType = 'text',
      images = [],
      buttons = []
    } = req.body;

    // Validate required fields
    if (!accountId || !conversationId || !content) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: accountId, conversationId, content'
      });
    }

    // Tạo message formatting nếu có
    let messageFormatting: MessageFormatting | undefined;
    if (messageType === 'formatted' && (images.length > 0 || buttons.length > 0)) {
      messageFormatting = {
        images: images || [],
        buttons: buttons || []
      };
    }

    // Gửi tin nhắn
    const result = await sendChatwootMessage({
      accountId,
      conversationId,
      content,
      messageType,
      messageFormatting
    });

    return res.json({
      success: result.success,
      data: {
        imagesSent: result.imagesSent || 0,
        textSent: result.textSent || false,
        messageType,
        hasImages: (images?.length || 0) > 0,
        hasButtons: (buttons?.length || 0) > 0
      },
      error: result.error
    });

  } catch (error: any) {
    console.error('❌ Lỗi test send follow-up message:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Test tạo follow-up job
 * POST /api/test/follow-up/create-job
 */
export const testCreateFollowUpJob = async (req: Request, res: Response) => {
  try {
    const {
      conversationId,
      tenantId,
      channelId,
      accountId,
      ruleIndex = 0,
      resetState = false
    } = req.body;

    // Validate required fields
    if (!conversationId || !tenantId || !channelId || !accountId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: conversationId, tenantId, channelId, accountId'
      });
    }

    // Tạo follow-up job
    const result = await createFollowUpJob({
      conversation_id: conversationId,
      tenant_id: tenantId,
      channel_id: channelId,
      account_id: accountId,
      rule_index: ruleIndex,
      reset_state: resetState
    });

    return res.json({
      success: result.success,
      data: result.data,
      message: result.message,
      error: result.error
    });

  } catch (error: any) {
    console.error('❌ Lỗi test create follow-up job:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Lấy cấu hình follow-up của channel
 * GET /api/test/follow-up/config/:channelId
 */
export const testGetFollowUpConfig = async (req: Request, res: Response) => {
  try {
    const { channelId } = req.params;

    if (!channelId) {
      return res.status(400).json({
        success: false,
        error: 'Missing channelId parameter'
      });
    }

    const config = await getChannelFollowUpConfig(channelId);

    return res.json({
      success: true,
      data: config,
      rulesCount: config?.followup_rules?.length || 0
    });

  } catch (error: any) {
    console.error('❌ Lỗi test get follow-up config:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Lấy trạng thái follow-up của conversation
 * GET /api/test/follow-up/state/:conversationId
 */
export const testGetFollowUpState = async (req: Request, res: Response) => {
  try {
    const { conversationId } = req.params;

    if (!conversationId) {
      return res.status(400).json({
        success: false,
        error: 'Missing conversationId parameter'
      });
    }

    const state = await getConversationFollowUpState(conversationId);

    return res.json({
      success: true,
      data: state,
      hasActiveJob: !!state?.active_job_id,
      isActive: state?.is_active || false
    });

  } catch (error: any) {
    console.error('❌ Lỗi test get follow-up state:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Hủy follow-up jobs
 * POST /api/test/follow-up/cancel
 */
export const testCancelFollowUpJobs = async (req: Request, res: Response) => {
  try {
    const { conversationId, tenantId, reason = 'Test cancellation' } = req.body;

    if (!conversationId || !tenantId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: conversationId, tenantId'
      });
    }

    const result = await cancelFollowUpJobs({
      conversation_id: conversationId,
      tenant_id: tenantId,
      reason
    });

    return res.json({
      success: result.success,
      data: result.data,
      message: result.message,
      error: result.error
    });

  } catch (error: any) {
    console.error('❌ Lỗi test cancel follow-up jobs:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Test gửi tin nhắn với sample data
 * POST /api/test/follow-up/send-sample
 */
export const testSendSampleMessage = async (req: Request, res: Response) => {
  try {
    const { accountId, conversationId, sampleType = 'text' } = req.body;

    if (!accountId || !conversationId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: accountId, conversationId'
      });
    }

    let messageData: any = {
      accountId,
      conversationId,
      content: '',
      messageType: 'text'
    };

    // Tạo sample data dựa trên type
    switch (sampleType) {
      case 'text':
        messageData.content = 'Đây là tin nhắn follow-up text đơn giản';
        break;

      case 'image':
        messageData.content = 'Đây là tin nhắn follow-up có hình ảnh';
        messageData.messageType = 'formatted';
        messageData.images = ['https://supabase.mooly.vn/storage/v1/object/public/product-loma/hero-landing-page-banner/loma-bag-bst-ha.jpg'];
        break;

      case 'button':
        messageData.content = 'Chọn một trong các tùy chọn bên dưới';
        messageData.messageType = 'formatted';
        messageData.buttons = [
          {
            id: 'btn_1',
            title: 'Tùy chọn 1',
            type: 'postback',
            payload: 'OPTION_1'
          },
          {
            id: 'btn_2',
            title: 'Xem website',
            type: 'link',
            url: 'https://mooly.vn'
          }
        ];
        break;

      case 'full':
        messageData.content = 'Tin nhắn đầy đủ với hình ảnh và nút';
        messageData.messageType = 'formatted';
        messageData.images = ['https://supabase.mooly.vn/storage/v1/object/public/product-loma/hero-landing-page-banner/loma-bag-bst-ha.jpg'];
        messageData.buttons = [
          {
            id: 'btn_full_1',
            title: 'Mua ngay',
            type: 'link',
            url: 'https://mooly.vn/buy'
          },
          {
            id: 'btn_full_2',
            title: 'Tư vấn',
            type: 'postback',
            payload: 'CONSULT'
          }
        ];
        break;

      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid sampleType. Use: text, image, button, full'
        });
    }

    // Gửi tin nhắn sample
    const result = await testSendFollowUpMessage({ body: messageData } as Request, res);
    return result;

  } catch (error: any) {
    console.error('❌ Lỗi test send sample message:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

export default {
  testSendFollowUpMessage,
  testCreateFollowUpJob,
  testGetFollowUpConfig,
  testGetFollowUpState,
  testCancelFollowUpJobs,
  testSendSampleMessage
};
