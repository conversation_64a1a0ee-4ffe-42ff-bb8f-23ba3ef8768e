# Follow-up Test API Documentation

## Tổng quan

API test cho hệ thống follow-up messages, cho phép test các tính năng:
- G<PERSON>i tin nhắn follow-up với hình ảnh và nút
- Tạo và quản lý follow-up jobs
- <PERSON><PERSON><PERSON> tra cấu hình và trạng thái

## Base URL

```
http://localhost:3000/api/test/follow-up
```

## API Endpoints

### 1. Gửi tin nhắn follow-up đơn lẻ

**POST** `/send-message`

Test gửi tin nhắn follow-up với các định dạng khác nhau.

#### Request Body:
```json
{
  "accountId": "string",
  "conversationId": "string", 
  "content": "string",
  "messageType": "text|formatted",
  "images": ["string"],
  "buttons": [
    {
      "id": "string",
      "title": "string",
      "type": "link|postback",
      "url": "string",
      "payload": "string"
    }
  ]
}
```

#### Ví dụ - Tin nhắn text đơn giản:
```bash
curl -X POST http://localhost:3000/api/test/follow-up/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "1",
    "conversationId": "123",
    "content": "Xin chào! Đây là tin nhắn follow-up test",
    "messageType": "text"
  }'
```

#### Ví dụ - Tin nhắn có hình ảnh:
```bash
curl -X POST http://localhost:3000/api/test/follow-up/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "1",
    "conversationId": "123",
    "content": "Đây là sản phẩm mới của chúng tôi",
    "messageType": "formatted",
    "images": ["https://picsum.photos/400/300"]
  }'
```

#### Ví dụ - Tin nhắn có nút:
```bash
curl -X POST http://localhost:3000/api/test/follow-up/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "1",
    "conversationId": "123",
    "content": "Chọn một trong các tùy chọn bên dưới",
    "messageType": "formatted",
    "buttons": [
      {
        "id": "btn_1",
        "title": "Xem sản phẩm",
        "type": "link",
        "url": "https://mooly.vn/products"
      },
      {
        "id": "btn_2", 
        "title": "Liên hệ tư vấn",
        "type": "postback",
        "payload": "CONTACT_SUPPORT"
      }
    ]
  }'
```

#### Ví dụ - Tin nhắn đầy đủ (hình ảnh + nút):
```bash
curl -X POST http://localhost:3000/api/test/follow-up/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "1",
    "conversationId": "123",
    "content": "Sản phẩm hot nhất tháng này!",
    "messageType": "formatted",
    "images": ["https://picsum.photos/400/300"],
    "buttons": [
      {
        "id": "btn_buy",
        "title": "Mua ngay",
        "type": "link",
        "url": "https://mooly.vn/buy"
      },
      {
        "id": "btn_info",
        "title": "Xem thông tin",
        "type": "postback", 
        "payload": "VIEW_INFO"
      }
    ]
  }'
```

### 2. Gửi tin nhắn sample

**POST** `/send-sample`

Gửi tin nhắn với dữ liệu mẫu có sẵn.

#### Request Body:
```json
{
  "accountId": "string",
  "conversationId": "string",
  "sampleType": "text|image|button|full"
}
```

#### Ví dụ:
```bash
# Text đơn giản
curl -X POST http://localhost:3000/api/test/follow-up/send-sample \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "1",
    "conversationId": "123",
    "sampleType": "text"
  }'

# Tin nhắn có hình ảnh
curl -X POST http://localhost:3000/api/test/follow-up/send-sample \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "1",
    "conversationId": "123", 
    "sampleType": "image"
  }'

# Tin nhắn có nút
curl -X POST http://localhost:3000/api/test/follow-up/send-sample \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "1",
    "conversationId": "123",
    "sampleType": "button"
  }'

# Tin nhắn đầy đủ
curl -X POST http://localhost:3000/api/test/follow-up/send-sample \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "1",
    "conversationId": "123",
    "sampleType": "full"
  }'
```

### 3. Tạo follow-up job

**POST** `/create-job`

Tạo job follow-up automation cho conversation.

#### Request Body:
```json
{
  "conversationId": "string",
  "tenantId": "string", 
  "channelId": "string",
  "accountId": "string",
  "ruleIndex": 0,
  "resetState": false
}
```

#### Ví dụ:
```bash
curl -X POST http://localhost:3000/api/test/follow-up/create-job \
  -H "Content-Type: application/json" \
  -d '{
    "conversationId": "123",
    "tenantId": "tenant-uuid",
    "channelId": "channel-uuid", 
    "accountId": "1",
    "ruleIndex": 0,
    "resetState": true
  }'
```

### 4. Lấy cấu hình follow-up

**GET** `/config/:channelId`

Lấy cấu hình follow-up rules của channel.

#### Ví dụ:
```bash
curl http://localhost:3000/api/test/follow-up/config/channel-uuid
```

### 5. Lấy trạng thái follow-up

**GET** `/state/:conversationId`

Lấy trạng thái follow-up hiện tại của conversation.

#### Ví dụ:
```bash
curl http://localhost:3000/api/test/follow-up/state/123
```

### 6. Hủy follow-up jobs

**POST** `/cancel`

Hủy tất cả follow-up jobs của conversation.

#### Request Body:
```json
{
  "conversationId": "string",
  "tenantId": "string",
  "reason": "string"
}
```

#### Ví dụ:
```bash
curl -X POST http://localhost:3000/api/test/follow-up/cancel \
  -H "Content-Type: application/json" \
  -d '{
    "conversationId": "123",
    "tenantId": "tenant-uuid",
    "reason": "Test cancellation"
  }'
```

## Response Format

Tất cả API đều trả về format:

```json
{
  "success": true|false,
  "data": {},
  "error": "string",
  "message": "string"
}
```

### Response cho send-message:
```json
{
  "success": true,
  "data": {
    "imagesSent": 1,
    "textSent": true,
    "messageType": "formatted",
    "hasImages": true,
    "hasButtons": true
  }
}
```

## Lưu ý quan trọng

1. **Account ID**: Sử dụng account ID thực từ Chatwoot
2. **Conversation ID**: Sử dụng conversation ID thực đang hoạt động
3. **Images**: URL hình ảnh phải accessible từ internet
4. **Buttons**: 
   - Link button cần có `url`
   - Postback button cần có `payload`
5. **Testing**: Kiểm tra trong Chatwoot để xem tin nhắn đã được gửi

## Troubleshooting

### Lỗi 400 - Missing required fields
Kiểm tra lại các trường bắt buộc trong request body.

### Lỗi 500 - Cannot send message
- Kiểm tra API token Chatwoot
- Kiểm tra account ID và conversation ID
- Kiểm tra URL hình ảnh

### Tin nhắn không hiển thị
- Kiểm tra conversation có đang active không
- Kiểm tra API token có quyền gửi tin nhắn không
